{"name": "client", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "tsc --noEmit && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint .", "prepare": "husky"}, "dependencies": {"@tailwindcss/vite": "^4.1.10", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "better-auth": "^1.2.12", "bootstrap": "^5.3.7", "lucide-react": "^0.525.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-modal": "^3.16.3", "react-router-dom": "^6.30.1", "shared": "workspace:*", "tailwindcss": "^4.1.10", "web-vitals": "^2.1.4"}, "devDependencies": {"@biomejs/biome": "2.0.5", "@eslint/js": "^9.30.1", "@testing-library/jest-dom": "^6.6.3", "@types/jest": "^27.5.2", "@types/node": "^24.0.4", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@types/react-modal": "^3.16.3", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "@vitejs/plugin-react": "^4.6.0", "eslint": "^9.30.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^15.5.2", "prettier": "^3.6.2", "typescript": "^5.8.3", "typescript-eslint": "^8.36.0", "vite": "^6.3.5", "vitest": "^3.2.4"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["prettier --write"]}}