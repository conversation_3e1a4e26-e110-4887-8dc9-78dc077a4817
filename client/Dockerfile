# Build stage
FROM oven/bun:1.1.42 AS builder

WORKDIR /app

# Copy workspace files
COPY tsconfig.json ./
COPY shared/package.json ./shared/
COPY client/package.json ./client/

# Create minimal package.json for client build only
RUN echo '{"name":"sumopod-client-build","workspaces":["./client","./shared"],"scripts":{"build:shared":"cd shared && bun run build","build:client":"cd client && bun run build"}}' > package.json

# Install dependencies
RUN bun install

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Copy client source code (excluding .env files)
COPY client ./client

# Remove any .env files that might have been copied (security measure)
RUN find /app -name "*.env*" -type f -delete

# Build the client app
RUN cd client && bun run build

# Production stage - nginx with environment variables
FROM nginx:alpine AS production

# Install gettext for envsubst
RUN apk add --no-cache gettext

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy nginx configuration
COPY client/nginx.conf /etc/nginx/nginx.conf

# Remove default nginx config
RUN rm -f /etc/nginx/conf.d/default.conf

# Copy environment template
COPY client/public/env.template.js /usr/share/nginx/html/env.template.js

# Create entrypoint script
COPY client/docker-entrypoint /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint

EXPOSE 80

ENTRYPOINT ["docker-entrypoint"]
CMD ["nginx", "-g", "daemon off;"]
