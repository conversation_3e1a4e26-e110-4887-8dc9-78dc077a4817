# Build stage
FROM oven/bun:1.1.42 AS builder

WORKDIR /app

# Copy workspace files
COPY tsconfig.json ./
COPY shared/package.json ./shared/
COPY client/package.json ./client/

# Create minimal package.json for client build only
RUN echo '{"name":"sumopod-client-build","workspaces":["./client","./shared"],"scripts":{"build:shared":"cd shared && bun run build","build:client":"cd client && bun run build"}}' > package.json

# Install dependencies
RUN bun install

# Copy shared workspace and build it
COPY shared ./shared
RUN cd shared && bun run build

# Copy client source code
COPY client ./client

# Build the client app
RUN cd client && bun run build

# Production stage - nginx with hardcoded values
FROM nginx:alpine AS production

# Copy built files to nginx
COPY --from=builder /app/client/dist /usr/share/nginx/html

# Copy nginx configuration
COPY client/nginx.conf /etc/nginx/nginx.conf

# Remove default nginx config
RUN rm -f /etc/nginx/conf.d/default.conf

# Create hardcoded env.js file
RUN echo 'window.ENV = {' > /usr/share/nginx/html/env.js && \
    echo '  API_URL: "https://sumopod-backend.rl5j77.easypanel.host",' >> /usr/share/nginx/html/env.js && \
    echo '  GOLD_API: "https://logam-mulia-api.vercel.app/prices/anekalogam",' >> /usr/share/nginx/html/env.js && \
    echo '  APP_NAME: "Sumopod"' >> /usr/share/nginx/html/env.js && \
    echo '};' >> /usr/share/nginx/html/env.js

EXPOSE 80

# Simple nginx start - no custom entrypoint
CMD ["nginx", "-g", "daemon off;"]
